import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Eye, 
  Edit, 
  Star, 
  Phone,
  Mail,
  MapPin,
  Users,
  ArrowUpDown,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import type { Vendor } from '@/types/vendor';

interface VendorTableProps {
  vendors: Vendor[];
  onView?: (id: string) => void;
  onEdit?: (id: string) => void;
  onSort?: (field: keyof Vendor) => void;
  sortField?: keyof Vendor;
  sortDirection?: 'asc' | 'desc';
  className?: string;
}

export const VendorTable: React.FC<VendorTableProps> = ({
  vendors,
  onView,
  onEdit,
  onSort,
  sortField,
  sortDirection,
  className
}) => {
  const getStatusColor = (status: Vendor['status']) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'inactive':
        return 'bg-gray-100 text-gray-800';
      case 'suspended':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: Vendor['status']) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'inactive':
        return <AlertTriangle className="h-4 w-4 text-gray-600" />;
      case 'suspended':
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-600" />;
    }
  };

  const getBBBEELevel = (level: number) => {
    if (level >= 1 && level <= 4) return 'bg-green-100 text-green-800';
    if (level >= 5 && level <= 6) return 'bg-yellow-100 text-yellow-800';
    if (level >= 7 && level <= 8) return 'bg-orange-100 text-orange-800';
    return 'bg-gray-100 text-gray-800';
  };

  const SortableHeader: React.FC<{ field: keyof Vendor; children: React.ReactNode }> = ({ field, children }) => (
    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
      {onSort ? (
        <button
          onClick={() => onSort(field)}
          className="flex items-center gap-1 hover:text-gray-700"
        >
          {children}
          <ArrowUpDown className="h-3 w-3" />
        </button>
      ) : (
        children
      )}
    </th>
  );

  return (
    <div className={cn("w-full overflow-x-auto", className)}>
      <table className="w-full min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <SortableHeader field="name">Vendor Name</SortableHeader>
            <SortableHeader field="contactPerson">Contact</SortableHeader>
            <SortableHeader field="city">Location</SortableHeader>
            <SortableHeader field="status">Status</SortableHeader>
            <SortableHeader field="rating">Rating</SortableHeader>
            <SortableHeader field="bbbeeLevel">BBBEE Level</SortableHeader>
            <SortableHeader field="totalJobs">Jobs</SortableHeader>
            <SortableHeader field="averageCost">Avg Cost</SortableHeader>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Services
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {vendors.map((vendor) => (
            <tr key={vendor.id} className="hover:bg-gray-50">
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  <Users className="h-5 w-5 text-blue-700 mr-3" />
                  <div>
                    <div className="font-medium text-gray-900">
                      {vendor.name}
                    </div>
                    <div className="text-sm text-gray-500">
                      {vendor.registrationNumber || 'N/A'}
                    </div>
                  </div>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm text-gray-900">
                  {vendor.contactPerson}
                </div>
                <div className="text-sm text-gray-500 flex items-center gap-1">
                  <Phone className="h-3 w-3" />
                  {vendor.phone}
                </div>
                <div className="text-sm text-gray-500 flex items-center gap-1">
                  <Mail className="h-3 w-3" />
                  {vendor.email}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center text-sm text-gray-900">
                  <MapPin className="h-4 w-4 mr-1" />
                  <div>
                    <div>{vendor.city}</div>
                    <div className="text-gray-500">{vendor.province}</div>
                  </div>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center gap-2">
                  {getStatusIcon(vendor.status)}
                  <Badge className={getStatusColor(vendor.status)}>
                    {vendor.status}
                  </Badge>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center gap-1">
                  <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  <span className="text-sm text-gray-900">
                    {vendor.rating ? vendor.rating.toFixed(1) : 'N/A'}
                  </span>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <Badge className={getBBBEELevel(vendor.bbbeeLevel)}>
                  Level {vendor.bbbeeLevel}
                </Badge>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm text-gray-900">
                  {vendor.completedJobs || 0} / {vendor.totalJobs || 0}
                </div>
                <div className="text-xs text-gray-500">
                  {vendor.completedJobs && vendor.totalJobs 
                    ? `${Math.round((vendor.completedJobs / vendor.totalJobs) * 100)}% success`
                    : 'No data'
                  }
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <span className="text-sm text-gray-900">
                  R{vendor.averageCost ? vendor.averageCost.toLocaleString() : 'N/A'}
                </span>
              </td>
              <td className="px-6 py-4">
                <div className="flex flex-wrap gap-1 max-w-xs">
                  {vendor.services?.slice(0, 2).map((service, index) => {
                    // Handle both string and object service formats
                    const serviceName = typeof service === 'string' ? service : service.name;
                    return (
                      <span
                        key={index}
                        className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs"
                      >
                        {serviceName}
                      </span>
                    );
                  })}
                  {vendor.services && vendor.services.length > 2 && (
                    <span className="text-xs text-gray-500">
                      +{vendor.services.length - 2} more
                    </span>
                  )}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex gap-2">
                  {onView && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onView(vendor.id)}
                      aria-label={`View ${vendor.name}`}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  )}
                  {onEdit && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onEdit(vendor.id)}
                      aria-label={`Edit ${vendor.name}`}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
      
      {vendors.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500">No vendors found</p>
        </div>
      )}
    </div>
  );
};
