import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Building2,
  Users,
  Plus,
  Minus,
  ChevronDown,
  ChevronRight,
  ArrowLeft,
  User,
  Crown,
  Shield,
  UserCheck,
  AlertCircle,
  Save
} from 'lucide-react';

interface HierarchyNode {
  id: string;
  name: string;
  roleType: string;
  assignedUser?: string;
  children: HierarchyNode[];
  expanded: boolean;
  level: number;
}

interface User {
  id: string;
  name: string;
  email: string;
  available: boolean;
}

const OrganizationalHierarchyBuilder: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const customerData = location.state?.customerData;
  const fleetData = location.state?.fleetData;
  const documents = location.state?.documents;

  // Sample users for assignment
  const [availableUsers] = useState<User[]>([
    { id: '1', name: 'John Smith', email: '<EMAIL>', available: true },
    { id: '2', name: '<PERSON> <PERSON>', email: '<EMAIL>', available: true },
    { id: '3', name: 'Michael Brown', email: '<EMAIL>', available: true },
    { id: '4', name: 'Lisa Davis', email: '<EMAIL>', available: true },
    { id: '5', name: 'David Wilson', email: '<EMAIL>', available: true },
  ]);

  // Role types available in the system
  const roleTypes = [
    { value: 'department_head', label: 'Department Head', icon: <Crown className="h-4 w-4" /> },
    { value: 'supervisor', label: 'Supervisor', icon: <Shield className="h-4 w-4" /> },
    { value: 'officer', label: 'Officer', icon: <UserCheck className="h-4 w-4" /> },
    { value: 'coordinator', label: 'Coordinator', icon: <User className="h-4 w-4" /> },
    { value: 'driver', label: 'Driver', icon: <User className="h-4 w-4" /> },
  ];

  // Initial hierarchy structure
  const [hierarchy, setHierarchy] = useState<HierarchyNode[]>([
    {
      id: '1',
      name: 'Department Head',
      roleType: 'department_head',
      children: [
        {
          id: '2',
          name: 'Fleet Supervisor',
          roleType: 'supervisor',
          children: [
            {
              id: '3',
              name: 'Fleet Officer',
              roleType: 'officer',
              children: [],
              expanded: false,
              level: 2
            }
          ],
          expanded: true,
          level: 1
        },
        {
          id: '4',
          name: 'Operations Supervisor',
          roleType: 'supervisor',
          children: [
            {
              id: '5',
              name: 'Operations Coordinator',
              roleType: 'coordinator',
              children: [],
              expanded: false,
              level: 2
            }
          ],
          expanded: true,
          level: 1
        }
      ],
      expanded: true,
      level: 0
    }
  ]);

  const [newRoleName, setNewRoleName] = useState('');
  const [newRoleType, setNewRoleType] = useState('');
  const [selectedParentId, setSelectedParentId] = useState<string>('top-level');

  // Find node by ID in the hierarchy
  const findNode = (nodes: HierarchyNode[], id: string): HierarchyNode | null => {
    for (const node of nodes) {
      if (node.id === id) return node;
      const found = findNode(node.children, id);
      if (found) return found;
    }
    return null;
  };

  // Toggle node expansion
  const toggleExpansion = (nodeId: string) => {
    const updateNodes = (nodes: HierarchyNode[]): HierarchyNode[] => {
      return nodes.map(node => {
        if (node.id === nodeId) {
          return { ...node, expanded: !node.expanded };
        }
        return { ...node, children: updateNodes(node.children) };
      });
    };
    setHierarchy(updateNodes(hierarchy));
  };

  // Add new role
  const addRole = () => {
    if (!newRoleName || !newRoleType) return;

    const newNode: HierarchyNode = {
      id: Date.now().toString(),
      name: newRoleName,
      roleType: newRoleType,
      children: [],
      expanded: false,
      level: selectedParentId === 'top-level'
        ? 0
        : (findNode(hierarchy, selectedParentId)?.level || -1) + 1
    };

    if (selectedParentId && selectedParentId !== 'top-level') {
      const updateNodes = (nodes: HierarchyNode[]): HierarchyNode[] => {
        return nodes.map(node => {
          if (node.id === selectedParentId) {
            return {
              ...node,
              children: [...node.children, newNode],
              expanded: true
            };
          }
          return { ...node, children: updateNodes(node.children) };
        });
      };
      setHierarchy(updateNodes(hierarchy));
    } else {
      setHierarchy([...hierarchy, newNode]);
    }

    setNewRoleName('');
    setNewRoleType('');
    setSelectedParentId('top-level');
  };

  // Remove role
  const removeRole = (nodeId: string) => {
    const removeFromNodes = (nodes: HierarchyNode[]): HierarchyNode[] => {
      return nodes.filter(node => node.id !== nodeId).map(node => ({
        ...node,
        children: removeFromNodes(node.children)
      }));
    };
    setHierarchy(removeFromNodes(hierarchy));
  };

  // Assign user to role
  const assignUser = (nodeId: string, userId: string) => {
    const updateNodes = (nodes: HierarchyNode[]): HierarchyNode[] => {
      return nodes.map(node => {
        if (node.id === nodeId) {
          return { ...node, assignedUser: userId === 'unassigned' ? undefined : userId };
        }
        return { ...node, children: updateNodes(node.children) };
      });
    };
    setHierarchy(updateNodes(hierarchy));
  };

  // Get all node IDs for parent selection
  const getAllNodeIds = (nodes: HierarchyNode[]): { id: string; name: string; level: number }[] => {
    const result: { id: string; name: string; level: number }[] = [];
    const traverse = (nodeList: HierarchyNode[]) => {
      nodeList.forEach(node => {
        result.push({ id: node.id, name: node.name, level: node.level });
        traverse(node.children);
      });
    };
    traverse(nodes);
    return result;
  };

  // Render hierarchy tree
  const renderNode = (node: HierarchyNode) => {
    const roleType = roleTypes.find(r => r.value === node.roleType);
    const assignedUser = availableUsers.find(u => u.id === node.assignedUser);
    const hasChildren = node.children.length > 0;

    return (
      <div key={node.id} className="space-y-2">
        <div 
          className="flex items-center gap-3 p-3 bg-white border rounded-lg hover:shadow-sm transition-shadow"
          style={{ marginLeft: `${node.level * 24}px` }}
        >
          {/* Expand/Collapse Button */}
          <Button
            variant="ghost"
            size="sm"
            className="p-1 h-6 w-6"
            onClick={() => toggleExpansion(node.id)}
            disabled={!hasChildren}
          >
            {hasChildren ? (
              node.expanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />
            ) : (
              <div className="h-4 w-4" />
            )}
          </Button>

          {/* Role Info */}
          <div className="flex items-center gap-2 flex-1">
            {roleType?.icon}
            <div>
              <p className="font-medium text-gray-900">{node.name}</p>
              <p className="text-sm text-gray-500">{roleType?.label}</p>
            </div>
          </div>

          {/* User Assignment */}
          <div className="flex items-center gap-2">
            <Select
              value={node.assignedUser || 'unassigned'}
              onValueChange={(value) => assignUser(node.id, value)}
            >
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Assign user" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="unassigned">No user assigned</SelectItem>
                {availableUsers.filter(u => u.available).map(user => (
                  <SelectItem key={user.id} value={user.id}>
                    <div className="flex flex-col">
                      <span>{user.name}</span>
                      <span className="text-xs text-gray-500">{user.email}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Remove Button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => removeRole(node.id)}
              className="text-red-600 hover:text-red-700"
            >
              <Minus className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Render Children */}
        {node.expanded && node.children.map(child => renderNode(child))}
      </div>
    );
  };

  const handleSaveAndContinue = () => {
    navigate('/fleet/customers/user-roles', {
      state: {
        customerData,
        fleetData,
        documents,
        hierarchy
      }
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Building2 className="h-8 w-8 text-blue-600" />
              <div>
                <h1 className="text-xl font-bold text-gray-900">Organizational Hierarchy Builder</h1>
                <p className="text-sm text-gray-600">
                  {customerData?.departmentName || 'Customer'} - Define Structure
                </p>
              </div>
            </div>
            <Button variant="outline" onClick={() => navigate(-1)}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto space-y-6">
          
          {/* Instructions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5 text-blue-600" />
                Define Organizational Structure
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 mb-4">
                Create your department's organizational hierarchy by defining roles and assigning users. 
                This structure will be used for approval workflows and access control.
              </p>
              
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  You can modify this structure later through the user management interface. 
                  Start with the basic hierarchy and add more roles as needed.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Hierarchy Tree View */}
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle>Current Hierarchy</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 max-h-96 overflow-y-auto">
                    {hierarchy.map(node => renderNode(node))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Add Role Form */}
            <div>
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Plus className="h-5 w-5 text-green-600" />
                    Add New Role
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="roleName">Role Name</Label>
                    <Input
                      id="roleName"
                      value={newRoleName}
                      onChange={(e) => setNewRoleName(e.target.value)}
                      placeholder="e.g., Fleet Manager"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="roleType">Role Type</Label>
                    <Select value={newRoleType} onValueChange={setNewRoleType}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select role type" />
                      </SelectTrigger>
                      <SelectContent>
                        {roleTypes.map(role => (
                          <SelectItem key={role.value} value={role.value}>
                            <div className="flex items-center gap-2">
                              {role.icon}
                              {role.label}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="parentRole">Parent Role (Optional)</Label>
                    <Select value={selectedParentId} onValueChange={setSelectedParentId}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select parent role" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="top-level">Top Level</SelectItem>
                        {getAllNodeIds(hierarchy).map(node => (
                          <SelectItem key={node.id} value={node.id}>
                            <span style={{ marginLeft: `${node.level * 12}px` }}>
                              {node.name}
                            </span>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <Button 
                    onClick={addRole}
                    disabled={!newRoleName || !newRoleType}
                    className="w-full"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Role
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-4">
            <Button variant="outline">
              <Save className="h-4 w-4 mr-2" />
              Save Draft
            </Button>
            <Button onClick={handleSaveAndContinue}>
              Continue to User Roles
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrganizationalHierarchyBuilder;
