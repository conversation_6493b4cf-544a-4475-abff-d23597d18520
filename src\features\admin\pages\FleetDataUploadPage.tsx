import React, { useState, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Upload,
  Download,
  FileSpreadsheet,
  CheckCircle,
  AlertCircle,
  ArrowLeft,
  Truck,
  Users,
  FileText,
  X
} from 'lucide-react';

interface UploadedData {
  vehicles: any[];
  drivers: any[];
  errors: ValidationError[];
}

interface ValidationError {
  row: number;
  column: string;
  message: string;
  severity: 'error' | 'warning';
}

const FleetDataUploadPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const customerData = location.state?.customerData;

  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [uploadedData, setUploadedData] = useState<UploadedData | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [dragActive, setDragActive] = useState(false);

  // Sample data for template
  const sampleVehicleData = [
    {
      'Registration Number': 'GP123ABC',
      'Make': 'Toyota',
      'Model': 'Hilux',
      'Year': '2022',
      'VIN': '1HGBH41JXMN109186',
      'Department': 'Transport',
      'Status': 'Active'
    },
    {
      'Registration Number': 'GP456DEF',
      'Make': 'Ford',
      'Model': 'Ranger',
      'Year': '2021',
      'VIN': '2HGBH41JXMN109187',
      'Department': 'Public Works',
      'Status': 'Active'
    }
  ];

  const sampleDriverData = [
    {
      'Employee ID': 'EMP001',
      'Full Name': 'John Smith',
      'License Number': '12345678901',
      'License Type': 'Code B',
      'Department': 'Transport',
      'Phone': '+27123456789',
      'Status': 'Active'
    },
    {
      'Employee ID': 'EMP002',
      'Full Name': 'Sarah Johnson',
      'License Number': '12345678902',
      'License Type': 'Code EB',
      'Department': 'Public Works',
      'Phone': '+27123456790',
      'Status': 'Active'
    }
  ];

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileUpload(e.dataTransfer.files[0]);
    }
  }, []);

  const handleFileUpload = async (file: File) => {
    if (!file) return;

    const allowedTypes = [
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/csv'
    ];

    if (!allowedTypes.includes(file.type)) {
      alert('Please upload a valid Excel (.xlsx, .xls) or CSV file');
      return;
    }

    setUploadedFile(file);
    setIsProcessing(true);

    try {
      // Simulate file processing
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Mock validation results
      const mockData: UploadedData = {
        vehicles: [
          { registrationNumber: 'GP123ABC', make: 'Toyota', model: 'Hilux', status: 'Valid' },
          { registrationNumber: 'GP456DEF', make: 'Ford', model: 'Ranger', status: 'Valid' },
          { registrationNumber: '', make: 'BMW', model: 'X3', status: 'Invalid' }
        ],
        drivers: [
          { employeeId: 'EMP001', name: 'John Smith', licenseNumber: '12345678901', status: 'Valid' },
          { employeeId: 'EMP002', name: 'Sarah Johnson', licenseNumber: '', status: 'Invalid' }
        ],
        errors: [
          { row: 3, column: 'Registration Number', message: 'Missing registration number', severity: 'error' },
          { row: 2, column: 'License Number', message: 'Missing license number', severity: 'error' },
          { row: 1, column: 'Year', message: 'Year should be 4 digits', severity: 'warning' }
        ]
      };

      setUploadedData(mockData);
    } catch (error) {
      console.error('Error processing file:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const downloadTemplate = (type: 'vehicles' | 'drivers') => {
    const data = type === 'vehicles' ? sampleVehicleData : sampleDriverData;
    const headers = Object.keys(data[0]);
    
    let csvContent = headers.join(',') + '\n';
    data.forEach(row => {
      csvContent += Object.values(row).join(',') + '\n';
    });

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${type}_template.csv`;
    link.click();
    window.URL.revokeObjectURL(url);
  };

  const handleContinue = () => {
    navigate('/fleet/customers/documents', {
      state: {
        customerData,
        fleetData: uploadedData
      }
    });
  };

  const removeFile = () => {
    setUploadedFile(null);
    setUploadedData(null);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <FileSpreadsheet className="h-8 w-8 text-blue-600" />
              <div>
                <h1 className="text-xl font-bold text-gray-900">Fleet Data Upload</h1>
                <p className="text-sm text-gray-600">
                  {customerData?.departmentName || 'Customer'} - Vehicle & Driver Data
                </p>
              </div>
            </div>
            <Button variant="outline" onClick={() => navigate(-1)}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto space-y-6">
          
          {/* Instructions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5 text-blue-600" />
                Upload Instructions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-gray-600">
                  Upload your fleet and driver data using Excel (.xlsx, .xls) or CSV files. 
                  Make sure your data follows the required format.
                </p>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <Truck className="h-5 w-5 text-blue-600" />
                      <span className="font-medium">Vehicle Data Template</span>
                    </div>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => downloadTemplate('vehicles')}
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Download
                    </Button>
                  </div>
                  
                  <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <Users className="h-5 w-5 text-green-600" />
                      <span className="font-medium">Driver Data Template</span>
                    </div>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => downloadTemplate('drivers')}
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Download
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Upload Area */}
          <Card>
            <CardHeader>
              <CardTitle>Upload Fleet Data</CardTitle>
            </CardHeader>
            <CardContent>
              <div
                className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                  dragActive 
                    ? 'border-blue-500 bg-blue-50' 
                    : 'border-gray-300 hover:border-gray-400'
                }`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
              >
                {uploadedFile ? (
                  <div className="space-y-4">
                    <div className="flex items-center justify-center gap-3">
                      <FileSpreadsheet className="h-8 w-8 text-green-600" />
                      <div className="text-left">
                        <p className="font-medium text-gray-900">{uploadedFile.name}</p>
                        <p className="text-sm text-gray-500">
                          {(uploadedFile.size / 1024 / 1024).toFixed(2)} MB
                        </p>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={removeFile}
                        className="text-red-600 hover:text-red-700"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                    
                    {isProcessing && (
                      <div className="flex items-center justify-center gap-2 text-blue-600">
                        <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
                        Processing file...
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="space-y-4">
                    <Upload className="h-12 w-12 text-gray-400 mx-auto" />
                    <div>
                      <p className="text-lg font-medium text-gray-900">
                        Drag and drop your file here
                      </p>
                      <p className="text-gray-500">or</p>
                      <Button 
                        variant="outline" 
                        className="mt-2"
                        onClick={() => document.getElementById('file-upload')?.click()}
                      >
                        Browse Files
                      </Button>
                      <input
                        id="file-upload"
                        type="file"
                        className="hidden"
                        accept=".xlsx,.xls,.csv"
                        onChange={(e) => e.target.files?.[0] && handleFileUpload(e.target.files[0])}
                      />
                    </div>
                    <p className="text-xs text-gray-500">
                      Supports Excel (.xlsx, .xls) and CSV files up to 10MB
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Data Preview and Validation */}
          {uploadedData && (
            <>
              {/* Validation Errors */}
              {uploadedData.errors.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-red-600">
                      <AlertCircle className="h-5 w-5" />
                      Validation Issues ({uploadedData.errors.length})
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2 max-h-60 overflow-y-auto">
                      {uploadedData.errors.map((error, index) => (
                        <Alert key={index} variant={error.severity === 'error' ? 'destructive' : 'default'}>
                          <AlertCircle className="h-4 w-4" />
                          <AlertDescription>
                            <span className="font-medium">Row {error.row}, {error.column}:</span> {error.message}
                          </AlertDescription>
                        </Alert>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Vehicle Data Preview */}
              {uploadedData.vehicles.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Truck className="h-5 w-5 text-blue-600" />
                      Vehicle Data Preview ({uploadedData.vehicles.length} records)
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="overflow-x-auto">
                      <table className="w-full border-collapse border border-gray-300">
                        <thead>
                          <tr className="bg-gray-50">
                            <th className="border border-gray-300 px-4 py-2 text-left">Registration</th>
                            <th className="border border-gray-300 px-4 py-2 text-left">Make</th>
                            <th className="border border-gray-300 px-4 py-2 text-left">Model</th>
                            <th className="border border-gray-300 px-4 py-2 text-left">Status</th>
                          </tr>
                        </thead>
                        <tbody>
                          {uploadedData.vehicles.slice(0, 5).map((vehicle, index) => (
                            <tr key={index} className={vehicle.status === 'Invalid' ? 'bg-red-50' : ''}>
                              <td className="border border-gray-300 px-4 py-2">
                                {vehicle.registrationNumber || (
                                  <span className="text-red-500 italic">Missing</span>
                                )}
                              </td>
                              <td className="border border-gray-300 px-4 py-2">{vehicle.make}</td>
                              <td className="border border-gray-300 px-4 py-2">{vehicle.model}</td>
                              <td className="border border-gray-300 px-4 py-2">
                                <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${
                                  vehicle.status === 'Valid'
                                    ? 'bg-green-100 text-green-800'
                                    : 'bg-red-100 text-red-800'
                                }`}>
                                  {vehicle.status === 'Valid' ? (
                                    <CheckCircle className="h-3 w-3" />
                                  ) : (
                                    <AlertCircle className="h-3 w-3" />
                                  )}
                                  {vehicle.status}
                                </span>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                      {uploadedData.vehicles.length > 5 && (
                        <p className="text-sm text-gray-500 mt-2">
                          Showing first 5 records. {uploadedData.vehicles.length - 5} more records available.
                        </p>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Driver Data Preview */}
              {uploadedData.drivers.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Users className="h-5 w-5 text-green-600" />
                      Driver Data Preview ({uploadedData.drivers.length} records)
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="overflow-x-auto">
                      <table className="w-full border-collapse border border-gray-300">
                        <thead>
                          <tr className="bg-gray-50">
                            <th className="border border-gray-300 px-4 py-2 text-left">Employee ID</th>
                            <th className="border border-gray-300 px-4 py-2 text-left">Name</th>
                            <th className="border border-gray-300 px-4 py-2 text-left">License Number</th>
                            <th className="border border-gray-300 px-4 py-2 text-left">Status</th>
                          </tr>
                        </thead>
                        <tbody>
                          {uploadedData.drivers.slice(0, 5).map((driver, index) => (
                            <tr key={index} className={driver.status === 'Invalid' ? 'bg-red-50' : ''}>
                              <td className="border border-gray-300 px-4 py-2">{driver.employeeId}</td>
                              <td className="border border-gray-300 px-4 py-2">{driver.name}</td>
                              <td className="border border-gray-300 px-4 py-2">
                                {driver.licenseNumber || (
                                  <span className="text-red-500 italic">Missing</span>
                                )}
                              </td>
                              <td className="border border-gray-300 px-4 py-2">
                                <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${
                                  driver.status === 'Valid'
                                    ? 'bg-green-100 text-green-800'
                                    : 'bg-red-100 text-red-800'
                                }`}>
                                  {driver.status === 'Valid' ? (
                                    <CheckCircle className="h-3 w-3" />
                                  ) : (
                                    <AlertCircle className="h-3 w-3" />
                                  )}
                                  {driver.status}
                                </span>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                      {uploadedData.drivers.length > 5 && (
                        <p className="text-sm text-gray-500 mt-2">
                          Showing first 5 records. {uploadedData.drivers.length - 5} more records available.
                        </p>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Action Buttons */}
              <div className="flex justify-end gap-4">
                <Button variant="outline" onClick={removeFile}>
                  Upload Different File
                </Button>
                <Button
                  onClick={handleContinue}
                  disabled={uploadedData.errors.some(e => e.severity === 'error')}
                  className="min-w-[120px]"
                >
                  Continue to Documents
                </Button>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default FleetDataUploadPage;
