import { Merchant } from '../../../types/merchant';
import { mockMerchants } from '../data/mockMerchants';

export class MerchantService {
  static async fetchMerchants(): Promise<Merchant[]> {
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // In a real application, this would be an API call
      // return await fetch('/api/merchants').then(res => res.json());
      
      return mockMerchants;
    } catch (error) {
      throw new Error('Failed to fetch merchants');
    }
  }
}
