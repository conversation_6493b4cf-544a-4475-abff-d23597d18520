import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Workflow,
  Plus,
  ArrowRight,
  ArrowLeft,
  Settings,
  Clock,
  DollarSign,
  User,
  CheckCircle,
  AlertCircle,
  Trash2,
  Save,
  RotateCcw
} from 'lucide-react';

interface WorkflowNode {
  id: string;
  type: 'start' | 'approval' | 'condition' | 'end';
  title: string;
  role?: string;
  spendLimit?: number;
  slaTime?: number;
  position: { x: number; y: number };
  connections: string[];
}

const ApprovalWorkflowEditor: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const customerData = location.state?.customerData;
  const fleetData = location.state?.fleetData;
  const documents = location.state?.documents;
  const hierarchy = location.state?.hierarchy;
  const users = location.state?.users;
  const linkedMerchants = location.state?.linkedMerchants;

  // Initial workflow nodes
  const [nodes, setNodes] = useState<WorkflowNode[]>([
    {
      id: '1',
      type: 'start',
      title: 'Request Submitted',
      position: { x: 50, y: 200 },
      connections: ['2']
    },
    {
      id: '2',
      type: 'approval',
      title: 'Supervisor Review',
      role: 'supervisor',
      spendLimit: 5000,
      slaTime: 24,
      position: { x: 250, y: 200 },
      connections: ['3']
    },
    {
      id: '3',
      type: 'condition',
      title: 'Amount > R10,000?',
      position: { x: 450, y: 200 },
      connections: ['4', '5']
    },
    {
      id: '4',
      type: 'approval',
      title: 'Department Head Approval',
      role: 'department_head',
      spendLimit: 50000,
      slaTime: 48,
      position: { x: 650, y: 150 },
      connections: ['5']
    },
    {
      id: '5',
      type: 'end',
      title: 'Request Approved',
      position: { x: 650, y: 250 },
      connections: []
    }
  ]);

  const [selectedNode, setSelectedNode] = useState<WorkflowNode | null>(null);
  const [showAddNodeModal, setShowAddNodeModal] = useState(false);
  const [newNodeType, setNewNodeType] = useState<WorkflowNode['type']>('approval');

  // Get available roles from hierarchy
  const getRoleOptions = () => {
    if (!hierarchy) return [];
    
    const extractRoles = (nodes: any[]): { value: string; label: string }[] => {
      const roles: { value: string; label: string }[] = [];
      nodes.forEach(node => {
        roles.push({ value: node.roleType, label: node.name });
        if (node.children) {
          roles.push(...extractRoles(node.children));
        }
      });
      return roles;
    };

    return extractRoles(hierarchy);
  };

  const getNodeIcon = (type: WorkflowNode['type']) => {
    switch (type) {
      case 'start': return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'approval': return <User className="h-5 w-5 text-blue-600" />;
      case 'condition': return <AlertCircle className="h-5 w-5 text-yellow-600" />;
      case 'end': return <CheckCircle className="h-5 w-5 text-green-600" />;
    }
  };

  const getNodeColor = (type: WorkflowNode['type']) => {
    switch (type) {
      case 'start': return 'bg-green-100 border-green-300';
      case 'approval': return 'bg-blue-100 border-blue-300';
      case 'condition': return 'bg-yellow-100 border-yellow-300';
      case 'end': return 'bg-green-100 border-green-300';
    }
  };

  const addNode = () => {
    const newNode: WorkflowNode = {
      id: Date.now().toString(),
      type: newNodeType,
      title: `New ${newNodeType.charAt(0).toUpperCase() + newNodeType.slice(1)}`,
      position: { x: 300, y: 300 },
      connections: []
    };

    setNodes([...nodes, newNode]);
    setShowAddNodeModal(false);
    setSelectedNode(newNode);
  };

  const updateNode = (nodeId: string, updates: Partial<WorkflowNode>) => {
    setNodes(nodes.map(node => 
      node.id === nodeId ? { ...node, ...updates } : node
    ));
    if (selectedNode?.id === nodeId) {
      setSelectedNode({ ...selectedNode, ...updates });
    }
  };

  const deleteNode = (nodeId: string) => {
    setNodes(prev => {
      const filtered = prev.filter(node => node.id !== nodeId);
      return filtered.map(node => ({
        ...node,
        connections: node.connections.filter(id => id !== nodeId)
      }));
    });
    if (selectedNode?.id === nodeId) {
      setSelectedNode(null);
    }
  };

  const resetWorkflow = () => {
    setNodes([
      {
        id: '1',
        type: 'start',
        title: 'Request Submitted',
        position: { x: 50, y: 200 },
        connections: ['2']
      },
      {
        id: '2',
        type: 'approval',
        title: 'Supervisor Review',
        role: 'supervisor',
        spendLimit: 5000,
        slaTime: 24,
        position: { x: 250, y: 200 },
        connections: ['3']
      },
      {
        id: '3',
        type: 'end',
        title: 'Request Approved',
        position: { x: 450, y: 200 },
        connections: []
      }
    ]);
    setSelectedNode(null);
  };

  const handleContinue = () => {
    navigate('/fleet/customers/progress-checklist', {
      state: {
        customerData,
        fleetData,
        documents,
        hierarchy,
        users,
        linkedMerchants,
        approvalWorkflow: nodes
      }
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Workflow className="h-8 w-8 text-blue-600" />
              <div>
                <h1 className="text-xl font-bold text-gray-900">Approval Workflow Editor</h1>
                <p className="text-sm text-gray-600">
                  {customerData?.departmentName || 'Customer'} - Configure Approval Process
                </p>
              </div>
            </div>
            <Button variant="outline" onClick={() => navigate(-1)}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto space-y-6">
          
          {/* Instructions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5 text-blue-600" />
                Configure Approval Workflow
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600 mb-2">
                    Design your department's approval workflow for work orders and maintenance requests. 
                    Define approval stages, spending limits, and SLA timeframes.
                  </p>
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      This workflow will be applied to all work orders. You can modify it later through the system settings.
                    </AlertDescription>
                  </Alert>
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" onClick={() => setShowAddNodeModal(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Node
                  </Button>
                  <Button variant="outline" onClick={resetWorkflow}>
                    <RotateCcw className="h-4 w-4 mr-2" />
                    Reset
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Workflow Canvas */}
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle>Workflow Diagram</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="relative bg-gray-50 rounded-lg p-6 min-h-96 overflow-auto">
                    {/* Workflow Nodes */}
                    {nodes.map((node, index) => (
                      <div key={node.id} className="absolute">
                        <div
                          className={`relative p-4 rounded-lg border-2 cursor-pointer transition-all ${getNodeColor(node.type)} ${
                            selectedNode?.id === node.id ? 'ring-2 ring-blue-500' : ''
                          }`}
                          style={{ 
                            left: node.position.x, 
                            top: node.position.y,
                            minWidth: '150px'
                          }}
                          onClick={() => setSelectedNode(node)}
                        >
                          <div className="flex items-center gap-2 mb-2">
                            {getNodeIcon(node.type)}
                            <span className="font-medium text-sm">{node.title}</span>
                          </div>
                          
                          {node.type === 'approval' && (
                            <div className="text-xs text-gray-600 space-y-1">
                              {node.role && <div>Role: {node.role}</div>}
                              {node.spendLimit && <div>Limit: R{node.spendLimit.toLocaleString()}</div>}
                              {node.slaTime && <div>SLA: {node.slaTime}h</div>}
                            </div>
                          )}

                          {/* Connection arrows */}
                          {node.connections.map((connectionId) => {
                            const targetNode = nodes.find(n => n.id === connectionId);
                            if (!targetNode) return null;
                            
                            return (
                              <div
                                key={connectionId}
                                className="absolute top-1/2 -right-6 transform -translate-y-1/2"
                              >
                                <ArrowRight className="h-4 w-4 text-gray-400" />
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Node Properties Panel */}
            <div>
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="h-5 w-5 text-blue-600" />
                    Node Properties
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {selectedNode ? (
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="nodeTitle">Node Title</Label>
                        <Input
                          id="nodeTitle"
                          value={selectedNode.title}
                          onChange={(e) => updateNode(selectedNode.id, { title: e.target.value })}
                        />
                      </div>

                      {selectedNode.type === 'approval' && (
                        <>
                          <div className="space-y-2">
                            <Label htmlFor="nodeRole">Approval Role</Label>
                            <Select 
                              value={selectedNode.role || ''} 
                              onValueChange={(value) => updateNode(selectedNode.id, { role: value })}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select role" />
                              </SelectTrigger>
                              <SelectContent>
                                {getRoleOptions().map(role => (
                                  <SelectItem key={role.value} value={role.value}>
                                    {role.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="spendLimit">Spend Limit (R)</Label>
                            <div className="relative">
                              <DollarSign className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                              <Input
                                id="spendLimit"
                                type="number"
                                value={selectedNode.spendLimit || ''}
                                onChange={(e) => updateNode(selectedNode.id, { spendLimit: parseInt(e.target.value) || 0 })}
                                className="pl-10"
                                placeholder="0"
                              />
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="slaTime">SLA Time (hours)</Label>
                            <div className="relative">
                              <Clock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                              <Input
                                id="slaTime"
                                type="number"
                                value={selectedNode.slaTime || ''}
                                onChange={(e) => updateNode(selectedNode.id, { slaTime: parseInt(e.target.value) || 0 })}
                                className="pl-10"
                                placeholder="24"
                              />
                            </div>
                          </div>
                        </>
                      )}

                      <div className="pt-4 border-t">
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => deleteNode(selectedNode.id)}
                          disabled={selectedNode.type === 'start' || selectedNode.type === 'end'}
                          className="w-full"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete Node
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      <Workflow className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                      <p>Select a node to edit its properties</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Workflow Summary */}
              <Card className="mt-4">
                <CardHeader>
                  <CardTitle className="text-lg">Workflow Summary</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Total Nodes:</span>
                      <span className="font-medium">{nodes.length}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Approval Steps:</span>
                      <span className="font-medium">{nodes.filter(n => n.type === 'approval').length}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Max SLA:</span>
                      <span className="font-medium">
                        {Math.max(0, ...nodes.filter(n => n.slaTime).map(n => n.slaTime || 0))}h
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-4">
            <Button variant="outline">
              <Save className="h-4 w-4 mr-2" />
              Save Draft
            </Button>
            <Button onClick={handleContinue}>
              Continue to Progress Review
            </Button>
          </div>
        </div>
      </div>

      {/* Add Node Modal */}
      {showAddNodeModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle>Add New Node</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="nodeType">Node Type</Label>
                <Select value={newNodeType} onValueChange={(value: WorkflowNode['type']) => setNewNodeType(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="approval">Approval Step</SelectItem>
                    <SelectItem value="condition">Condition Check</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex justify-end gap-4">
                <Button variant="outline" onClick={() => setShowAddNodeModal(false)}>
                  Cancel
                </Button>
                <Button onClick={addNode}>
                  Add Node
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default ApprovalWorkflowEditor;
