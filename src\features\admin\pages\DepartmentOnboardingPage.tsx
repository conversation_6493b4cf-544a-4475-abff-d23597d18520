import React, { useState } from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Building, 
  MapPin, 
  User, 
  CreditCard, 
  FileText,
  CheckCircle,
  ArrowLeft,
  ArrowRight,
  AlertCircle,
  Phone,
  Mail,
  Building2
} from 'lucide-react';

const departmentOnboardingSchema = z.object({
  // Entity Information
  entityName: z.string().min(2, 'Entity name must be at least 2 characters'),
  entityType: z.string().min(1, 'Please select entity type'),
  registrationNumber: z.string().min(1, 'Registration number is required'),
  
  // Address Information
  physicalAddress: z.string().min(1, 'Physical address is required'),
  postalAddress: z.string().min(1, 'Postal address is required'),
  city: z.string().min(1, 'City is required'),
  province: z.string().min(1, 'Please select a province'),
  postalCode: z.string().min(1, 'Postal code is required'),
  
  // Primary Official
  primaryOfficialName: z.string().min(2, 'Primary official name is required'),
  primaryOfficialTitle: z.string().min(2, 'Official title is required'),
  primaryOfficialEmail: z.string().email('Invalid email address'),
  primaryOfficialPhone: z.string().min(10, 'Phone number must be at least 10 digits'),
  
  // Secondary Official
  secondaryOfficialName: z.string().optional(),
  secondaryOfficialTitle: z.string().optional(),
  secondaryOfficialEmail: z.string().email('Invalid email').or(z.literal('')).optional(),
  secondaryOfficialPhone: z.string().optional(),
  
  // Billing Information
  billingCutOffDate: z.string().min(1, 'Billing cut-off date is required'),
  billingFrequency: z.string().min(1, 'Please select billing frequency'),
  billingContactName: z.string().min(2, 'Billing contact name is required'),
  billingContactEmail: z.string().email('Invalid billing email'),
  billingContactPhone: z.string().min(10, 'Billing phone is required'),
  
  // Department Mandate
  // Department Mandate
  departmentMandate: z.string().min(1, 'Department mandate is required'),
  budgetAllocation: z.string()
    .transform((val) => parseFloat(val))
    .refine((val) => !isNaN(val) && val >= 0, {
      message: 'Budget allocation must be a positive number',
    }),
  fleetSize: z.string()
    .transform((val) => parseInt(val, 10))
    .refine((val) => !isNaN(val) && val >= 1, {
      message: 'Expected fleet size must be at least 1',
    }),
});

type DepartmentOnboardingData = z.infer<typeof departmentOnboardingSchema>;

const DepartmentOnboardingPage: React.FC = () => {
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 5;
  
  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    setValue,
    trigger,
  } = useForm<DepartmentOnboardingData>({
    resolver: zodResolver(departmentOnboardingSchema),
    mode: 'onChange',
    defaultValues: {
      physicalAddress: '',
      postalAddress: '',
      city: '',
      province: '',
      postalCode: '',
    }
  });



  const stepValidation = {
    1: ['entityName', 'entityType', 'registrationNumber'],
    2: ['physicalAddress', 'postalAddress', 'city', 'province', 'postalCode'],
    3: ['primaryOfficialName', 'primaryOfficialTitle', 'primaryOfficialEmail', 'primaryOfficialPhone'],
    4: ['billingCutOffDate', 'billingFrequency', 'billingContactName', 'billingContactEmail', 'billingContactPhone'],
    5: ['departmentMandate', 'budgetAllocation', 'fleetSize'],
  };

  const nextStep = async () => {
    const fieldsToValidate = stepValidation[currentStep as keyof typeof stepValidation];
    const isValid = await trigger(fieldsToValidate as any);

    if (isValid && currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const onSubmit = async (data: DepartmentOnboardingData) => {
    setIsSubmitting(true);
    try {
      console.log('Submitting department onboarding application:', data);
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      navigate('/onboard/success', {
        state: { 
          entityName: data.entityName,
          applicationId: 'APP-' + Date.now() 
        }
      });
    } catch (error) {
      console.error('Failed to submit application:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const getStepTitle = (step: number) => {
    const titles = {
      1: 'Entity Information',
      2: 'Address Details',
      3: 'Contact Officials',
      4: 'Billing Information',
      5: 'Department Details'
    };
    return titles[step as keyof typeof titles];
  };

  const getStepIcon = (step: number) => {
    const icons = {
      1: <Building className="h-5 w-5" />,
      2: <MapPin className="h-5 w-5" />,
      3: <User className="h-5 w-5" />,
      4: <CreditCard className="h-5 w-5" />,
      5: <FileText className="h-5 w-5" />
    };
    return icons[step as keyof typeof icons];
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-3 sm:px-4 py-3 sm:py-4">
          <div className="flex items-center justify-between gap-2">
            <div className="flex items-center gap-2 sm:gap-3 min-w-0 flex-1">
              <Building2 className="h-6 w-6 sm:h-8 sm:w-8 text-blue-600 flex-shrink-0" />
              <div className="min-w-0">
                <h1 className="text-base sm:text-xl font-bold text-gray-900 truncate">RT46-2026 Fleet Management</h1>
                <p className="text-xs sm:text-sm text-gray-600">Department Application</p>
              </div>
            </div>
            <Button variant="outline" onClick={() => navigate('/')} className="text-xs sm:text-sm h-8 sm:h-10 px-2 sm:px-4">
              <ArrowLeft className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
              <span className="hidden sm:inline">Back to Home</span>
              <span className="sm:hidden">Back</span>
            </Button>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-3 sm:px-4 py-4 sm:py-6 lg:py-8 max-w-4xl">
        {/* Progress Section */}
        <div className="mb-6 sm:mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3 sm:mb-4 gap-2">
            <h2 className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900">Apply for Fleet Management Services</h2>
            <span className="text-xs sm:text-sm text-gray-500">Step {currentStep} of {totalSteps}</span>
          </div>

          <Progress value={(currentStep / totalSteps) * 100} className="mb-3 sm:mb-4" />

          {/* Step Indicators */}
          <div className="flex justify-between">
            {[1, 2, 3, 4, 5].map((step) => (
              <div key={step} className="flex flex-col items-center">
                <div className={`w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center text-xs sm:text-sm font-medium ${
                  step < currentStep
                    ? 'bg-green-600 text-white'
                    : step === currentStep
                    ? 'bg-blue-600 text-white' 
                    : 'bg-gray-200 text-gray-600'
                }`}>
                  {step < currentStep ? <CheckCircle className="h-5 w-5" /> : getStepIcon(step)}
                </div>
                <span className="text-xs text-gray-600 mt-1 text-center max-w-20">
                  {getStepTitle(step)}
                </span>
              </div>
            ))}
          </div>
        </div>

        <form onSubmit={handleSubmit(onSubmit)}>
          {/* Step 1: Entity Information */}
          {currentStep === 1 && (
            <Card className="w-full">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building className="h-5 w-5 text-blue-600" />
                  Entity Information
                </CardTitle>
                <p className="text-sm text-gray-600">
                  Provide basic information about your department or organization
                </p>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="entityName">Entity Name *</Label>
                    <Input
                      id="entityName"
                      {...register('entityName')}
                      placeholder="e.g., Department of Transport"
                      className={errors.entityName ? 'border-red-500' : ''}
                    />
                    {errors.entityName && (
                      <p className="text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="h-4 w-4" />
                        {errors.entityName.message}
                      </p>
                    )}
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="entityType">Entity Type *</Label>
                    <Controller
                      name="entityType"
                      control={control}
                      render={({ field }) => (
                        <Select onValueChange={field.onChange} value={field.value}>
                          <SelectTrigger className={errors.entityType ? 'border-red-500' : ''}>
                            <SelectValue placeholder="Select entity type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="national">National Department</SelectItem>
                            <SelectItem value="provincial">Provincial Department</SelectItem>
                            <SelectItem value="municipal">Municipal Department</SelectItem>
                            <SelectItem value="agency">Government Agency</SelectItem>
                          </SelectContent>
                        </Select>
                      )}
                    />
                    {errors.entityType && (
                      <p className="text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="h-4 w-4" />
                        {errors.entityType.message}
                      </p>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="registrationNumber">Registration Number *</Label>
                  <Input
                    id="registrationNumber"
                    {...register('registrationNumber')}
                    placeholder="Official registration or reference number"
                    className={errors.registrationNumber ? 'border-red-500' : ''}
                  />
                  {errors.registrationNumber && (
                    <p className="text-sm text-red-600 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      {errors.registrationNumber.message}
                    </p>
                  )}
                </div>

                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    Ensure all information matches your official government records for faster processing.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          )}

          {/* Step 2: Address Information */}
          {currentStep === 2 && (
            <Card className="w-full">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-5 w-5 text-blue-600" />
                  Address Information
                </CardTitle>
                <p className="text-sm text-gray-600">
                  Provide your department's official addresses
                </p>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="physicalAddress">Physical Address *</Label>
                  <Textarea
                    id="physicalAddress"
                    {...register('physicalAddress')}
                    placeholder="e.g., 123 Main Street, Suburb Name, City"
                    rows={3}
                    className={errors.physicalAddress ? 'border-red-500' : ''}
                  />
                  {errors.physicalAddress && (
                    <p className="text-sm text-red-600 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      {errors.physicalAddress.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="postalAddress">Postal Address *</Label>
                  <Textarea
                    id="postalAddress"
                    {...register('postalAddress')}
                    placeholder="e.g., P.O. Box 123, City Name, Province"
                    rows={3}
                    className={errors.postalAddress ? 'border-red-500' : ''}
                  />
                  {errors.postalAddress && (
                    <p className="text-sm text-red-600 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      {errors.postalAddress.message}
                    </p>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="city">City *</Label>
                    <Input
                      id="city"
                      {...register('city')}
                      placeholder="e.g., Cape Town, Johannesburg"
                      className={errors.city ? 'border-red-500' : ''}
                    />
                    {errors.city && (
                      <p className="text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="h-4 w-4" />
                        {errors.city.message}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="province">Province *</Label>
                    <Controller
                      name="province"
                      control={control}
                      render={({ field }) => (
                        <Select onValueChange={field.onChange} value={field.value}>
                          <SelectTrigger className={errors.province ? 'border-red-500' : ''}>
                            <SelectValue placeholder="Select province" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="eastern-cape">Eastern Cape</SelectItem>
                            <SelectItem value="free-state">Free State</SelectItem>
                            <SelectItem value="gauteng">Gauteng</SelectItem>
                            <SelectItem value="kwazulu-natal">KwaZulu-Natal</SelectItem>
                            <SelectItem value="limpopo">Limpopo</SelectItem>
                            <SelectItem value="mpumalanga">Mpumalanga</SelectItem>
                            <SelectItem value="northern-cape">Northern Cape</SelectItem>
                            <SelectItem value="north-west">North West</SelectItem>
                            <SelectItem value="western-cape">Western Cape</SelectItem>
                          </SelectContent>
                        </Select>
                      )}
                    />
                    {errors.province && (
                      <p className="text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="h-4 w-4" />
                        {errors.province.message}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="postalCode">Postal Code *</Label>
                    <Input
                      id="postalCode"
                      {...register('postalCode')}
                      placeholder="e.g., 8001, 2000, 0001"
                      className={errors.postalCode ? 'border-red-500' : ''}
                    />
                    {errors.postalCode && (
                      <p className="text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="h-4 w-4" />
                        {errors.postalCode.message}
                      </p>
                    )}
                  </div>
                </div>

                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Address Requirements:</strong> All address fields are required.
                    Please provide meaningful address information to ensure proper service delivery.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          )}

          {/* Step 3: Contact Officials */}
          {currentStep === 3 && (
            <div className="space-y-6">
              {/* Primary Official */}
              <Card className="w-full">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5 text-blue-600" />
                    Primary Official
                  </CardTitle>
                  <p className="text-sm text-gray-600">
                    Main contact person for this application
                  </p>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="primaryOfficialName">Full Name *</Label>
                      <Input
                        id="primaryOfficialName"
                        {...register('primaryOfficialName')}
                        placeholder="Full name"
                        className={errors.primaryOfficialName ? 'border-red-500' : ''}
                      />
                      {errors.primaryOfficialName && (
                        <p className="text-sm text-red-600 flex items-center gap-1">
                          <AlertCircle className="h-4 w-4" />
                          {errors.primaryOfficialName.message}
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="primaryOfficialTitle">Title/Position *</Label>
                      <Input
                        id="primaryOfficialTitle"
                        {...register('primaryOfficialTitle')}
                        placeholder="e.g., Director, Manager"
                        className={errors.primaryOfficialTitle ? 'border-red-500' : ''}
                      />
                      {errors.primaryOfficialTitle && (
                        <p className="text-sm text-red-600 flex items-center gap-1">
                          <AlertCircle className="h-4 w-4" />
                          {errors.primaryOfficialTitle.message}
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="primaryOfficialEmail">Email Address *</Label>
                      <div className="relative">
                        <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          id="primaryOfficialEmail"
                          type="email"
                          {...register('primaryOfficialEmail')}
                          placeholder="<EMAIL>"
                          className={`pl-10 ${errors.primaryOfficialEmail ? 'border-red-500' : ''}`}
                        />
                      </div>
                      {errors.primaryOfficialEmail && (
                        <p className="text-sm text-red-600 flex items-center gap-1">
                          <AlertCircle className="h-4 w-4" />
                          {errors.primaryOfficialEmail.message}
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="primaryOfficialPhone">Phone Number *</Label>
                      <div className="relative">
                        <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          id="primaryOfficialPhone"
                          type="tel"
                          {...register('primaryOfficialPhone')}
                          placeholder="+27 12 345 6789"
                          className={`pl-10 ${errors.primaryOfficialPhone ? 'border-red-500' : ''}`}
                        />
                      </div>
                      {errors.primaryOfficialPhone && (
                        <p className="text-sm text-red-600 flex items-center gap-1">
                          <AlertCircle className="h-4 w-4" />
                          {errors.primaryOfficialPhone.message}
                        </p>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Secondary Official */}
              <Card className="w-full">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5 text-gray-400" />
                    Secondary Official (Optional)
                  </CardTitle>
                  <p className="text-sm text-gray-600">
                    Alternative contact person
                  </p>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="secondaryOfficialName">Full Name</Label>
                      <Input
                        id="secondaryOfficialName"
                        {...register('secondaryOfficialName')}
                        placeholder="Full name (optional)"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="secondaryOfficialTitle">Title/Position</Label>
                      <Input
                        id="secondaryOfficialTitle"
                        {...register('secondaryOfficialTitle')}
                        placeholder="Title (optional)"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="secondaryOfficialEmail">Email Address</Label>
                      <div className="relative">
                        <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          id="secondaryOfficialEmail"
                          type="email"
                          {...register('secondaryOfficialEmail')}
                          placeholder="<EMAIL> (optional)"
                          className="pl-10"
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="secondaryOfficialPhone">Phone Number</Label>
                      <div className="relative">
                        <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          id="secondaryOfficialPhone"
                          type="tel"
                          {...register('secondaryOfficialPhone')}
                          placeholder="+27 12 345 6789 (optional)"
                          className="pl-10"
                        />
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Step 4: Billing Information */}
          {currentStep === 4 && (
            <Card className="w-full">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="h-5 w-5 text-blue-600" />
                  Billing Information
                </CardTitle>
                <p className="text-sm text-gray-600">
                  Provide billing preferences and contact details
                </p>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="billingCutOffDate">Billing Cut-off Date *</Label>
                    <Input
                      id="billingCutOffDate"
                      {...register('billingCutOffDate')}
                      placeholder="e.g., 25th of each month"
                      className={errors.billingCutOffDate ? 'border-red-500' : ''}
                    />
                    {errors.billingCutOffDate && (
                      <p className="text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="h-4 w-4" />
                        {errors.billingCutOffDate.message}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="billingFrequency">Billing Frequency *</Label>
                    <Controller
                      name="billingFrequency"
                      control={control}
                      render={({ field }) => (
                        <Select onValueChange={field.onChange} value={field.value}>
                          <SelectTrigger className={errors.billingFrequency ? 'border-red-500' : ''}>
                            <SelectValue placeholder="Select billing frequency" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="monthly">Monthly</SelectItem>
                            <SelectItem value="quarterly">Quarterly</SelectItem>
                            <SelectItem value="annually">Annually</SelectItem>
                          </SelectContent>
                        </Select>
                      )}
                    />
                    {errors.billingFrequency && (
                      <p className="text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="h-4 w-4" />
                        {errors.billingFrequency.message}
                      </p>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="billingContactName">Billing Contact Name *</Label>
                  <Input
                    id="billingContactName"
                    {...register('billingContactName')}
                    placeholder="Full name of billing contact"
                    className={errors.billingContactName ? 'border-red-500' : ''}
                  />
                  {errors.billingContactName && (
                    <p className="text-sm text-red-600 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      {errors.billingContactName.message}
                    </p>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="billingContactEmail">Email Address *</Label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="billingContactEmail"
                        type="email"
                        {...register('billingContactEmail')}
                        placeholder="<EMAIL>"
                        className={`pl-10 ${errors.billingContactEmail ? 'border-red-500' : ''}`}
                      />
                    </div>
                    {errors.billingContactEmail && (
                      <p className="text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="h-4 w-4" />
                        {errors.billingContactEmail.message}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="billingContactPhone">Phone Number *</Label>
                    <div className="relative">
                      <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="billingContactPhone"
                        type="tel"
                        {...register('billingContactPhone')}
                        placeholder="+27 12 345 6789"
                        className={`pl-10 ${errors.billingContactPhone ? 'border-red-500' : ''}`}
                      />
                    </div>
                    {errors.billingContactPhone && (
                      <p className="text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="h-4 w-4" />
                        {errors.billingContactPhone.message}
                      </p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Step 5: Department Details */}
          {currentStep === 5 && (
            <Card className="w-full">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5 text-blue-600" />
                  Department Details
                </CardTitle>
                <p className="text-sm text-gray-600">
                  Provide information about your department's mandate and requirements
                </p>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="departmentMandate">Department Mandate *</Label>
                  <Textarea
                    id="departmentMandate"
                    {...register('departmentMandate')}
                    placeholder="Describe your department's main responsibilities and objectives..."
                    rows={4}
                    className={errors.departmentMandate ? 'border-red-500' : ''}
                  />
                  {errors.departmentMandate && (
                    <p className="text-sm text-red-600 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      {errors.departmentMandate.message}
                    </p>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="budgetAllocation">Annual Budget Allocation (R) *</Label>
                    <Input
                      id="budgetAllocation"
                      type="number"
                      {...register('budgetAllocation')}
                      placeholder="0.00"
                      className={errors.budgetAllocation ? 'border-red-500' : ''}
                    />
                    {errors.budgetAllocation && (
                      <p className="text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="h-4 w-4" />
                        {errors.budgetAllocation.message}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="fleetSize">Expected Fleet Size *</Label>
                    <Input
                      id="fleetSize"
                      type="number"
                      {...register('fleetSize')}
                      placeholder="Number of vehicles"
                      className={errors.fleetSize ? 'border-red-500' : ''}
                    />
                    {errors.fleetSize && (
                      <p className="text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="h-4 w-4" />
                        {errors.fleetSize.message}
                      </p>
                    )}
                  </div>
                </div>

                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    This information will help us better understand your fleet management needs and provide appropriate services.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          )}

          {/* Navigation Buttons */}
          <div className="flex justify-between mt-8">
            <Button
              type="button"
              variant="outline"
              onClick={prevStep}
              disabled={currentStep === 1}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Previous
            </Button>

            {currentStep < totalSteps ? (
              <Button type="button" onClick={nextStep}>
                Next
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            ) : (
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? 'Submitting...' : 'Submit Application'}
              </Button>
            )}
          </div>
        </form>
      </div>
    </div>
  );
};

export default DepartmentOnboardingPage;


